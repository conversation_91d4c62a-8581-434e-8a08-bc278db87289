import { useMessageGetter } from '@messageformat/react';
import type { ChangeSignalSchema } from '@shape-construction/api/src';
import { Avatar, Button, CollapsableCard, Divider } from '@shape-construction/arch-ui';
import FileThumbnail from '@shape-construction/arch-ui/src/FileThumbnail/FileThumbnail';
import { IssueTrackerIcon, ShiftReportsIcon } from '@shape-construction/arch-ui/src/Icons/product-logo';
import { ChevronDownIcon, ChevronUpIcon, UnlinkIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { formatDate } from '@shape-construction/utils/DateTime';
import { truncatedLocationPath } from 'app/components/Utils/locations';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useProjectLocations } from 'app/queries/projects/locations';
import { useProjectPeople } from 'app/queries/projects/people';
import { useShiftReport } from 'app/queries/shiftReports/shiftReports';
import { Link } from 'react-router';
import { getSignalLink } from '../../change-signals/components/ChangeSignalsTable';

function renderSignalTypeWithIcon(
  signalType: ChangeSignalSchema['signalType'],
  issueTitle: ChangeSignalSchema['title']
) {
  const messages = useMessageGetter(
    'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.changeSignalsList'
  );
  if (signalType === 'issue') {
    return (
      <div className="flex items-center gap-1 text-xs leading-4 font-normal text-neutral-subtle">
        <IssueTrackerIcon height={12} width={12} />
        <span>{messages('issue')}</span>
        <span className="uppercase font-medium">{issueTitle}</span>
      </div>
    );
  }
  return (
    <div className="flex items-center gap-1 text-xs leading-4 font-normal text-neutral-subtle">
      <ShiftReportsIcon height={12} width={12} />
      <span>{messages('downtime')}</span>
    </div>
  );
}

type PotentialChangeSignalItemProps = {
  readonly signal: ChangeSignalSchema;
  onUnlinkChangeSignalClick: (
    signalId: ChangeSignalSchema['signalId'],
    signalType: ChangeSignalSchema['signalType']
  ) => void;
  isUnlinkingChangeSignals?: boolean;
  canUnlink: boolean;
};

export function PotentialChangeSignalItem({
  signal,
  onUnlinkChangeSignalClick,
  isUnlinkingChangeSignals,
  canUnlink,
}: PotentialChangeSignalItemProps) {
  const project = useCurrentProject();
  const { data: shiftReport } = useShiftReport(
    project.id,
    signal.signalType === 'downtime' ? signal.shiftReportId : ''
  );
  const messages = useMessageGetter('controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer');
  const { data: projectPeople } = useProjectPeople(project.id);
  const { data: locations } = useProjectLocations(project.id);

  const publishedDate = formatDate(signal.publishedAt, project.timezone, 'DD-MMM-YY');
  const locationName = locations?.find((location) => location.id === signal.locationId)?.name;
  const teamMember = projectPeople?.find((person) => person.id === signal.teamMemberId)?.user;

  return (
    <CollapsableCard className="rounded-sm border-neutral-subtle border-1 bg-neutral-subtlest">
      {({ isOpen }) => (
        <>
          <CollapsableCard.Header
            isOpen={isOpen}
            openIcon={ChevronDownIcon}
            closeIcon={ChevronUpIcon}
            className="flex gap-2 items-center"
          >
            {renderSignalTypeWithIcon(signal.signalType, signal.title)}
          </CollapsableCard.Header>
          <CollapsableCard.Actions>
            {canUnlink && (
              <Button
                aria-label={messages('unlink')}
                onClick={() => onUnlinkChangeSignalClick(signal.signalId, signal.signalType)}
                size="xs"
                color="danger"
                variant="text"
                leadingIcon={UnlinkIcon}
                disabled={isUnlinkingChangeSignals}
              >
                {messages('unlink')}
              </Button>
            )}
          </CollapsableCard.Actions>
          <CollapsableCard.Subheader>
            <div className="pl-10">
              <Link
                to={getSignalLink(signal, project.id)}
                className="text-link-brand text-sm leading-5 font-normal underline cursor-pointer"
              >
                {signal.title}
              </Link>
            </div>
          </CollapsableCard.Subheader>
          <CollapsableCard.Content>
            <Divider orientation={'horizontal'} />
            <div className="pt-4 flex flex-col gap-4 text-xs leading-4 font-normal">
              <div className="flex gap-4 sm:gap-12 grow flex-wrap">
                {/* Author */}
                <div className="flex flex-col gap-1">
                  <span className="text-neutral-subtle">Author</span>
                  <div className="flex gap-1">
                    <Avatar text={teamMember?.name?.slice(0, 2) || ''} size={'xs'} imgURL={teamMember?.avatarUrl} />
                    <span className="text-neutral">{teamMember?.name}</span>
                  </div>
                </div>
                {/* CreatedAt */}
                <div className="flex flex-col gap-1">
                  <span className="text-neutral-subtle">Created</span>
                  <div className="flex gap-1">
                    <span className="text-neutral">
                      {formatDate(signal.publishedAt, project.timezone, 'DD-MMM-YYYY')}
                    </span>
                  </div>
                </div>
                {/* Impact */}
                <div className="flex flex-col gap-1">
                  <span className="text-neutral-subtle">{signal.signalType === 'issue' ? 'Impact' : 'Shift type'}</span>
                  <div className="flex gap-1">
                    <span className="text-neutral">
                      {signal.signalType === 'issue' ? signal.impact : shiftReport?.shiftType}
                    </span>
                  </div>
                </div>
                {/* Status */}
                <div className="flex flex-col gap-1">
                  <span className="text-neutral-subtle">
                    {signal.signalType === 'issue' ? 'Status' : 'Time lost (hrs)'}
                  </span>
                  <div className="flex gap-1">
                    <span className="text-neutral">
                      {signal.signalType === 'issue' ? signal.issue.currentState : signal.downtime.timeLost}
                    </span>
                  </div>
                </div>
                {/* Reason */}
                {signal.signalType === 'downtime' && (
                  <div className="flex flex-col gap-1">
                    <span className="text-neutral-subtle">Reason</span>
                    <div className="flex gap-1 max-w-32">
                      <span className="text-neutral truncate" title={signal.downtime?.causalType ?? ''}>
                        {signal.downtime.causalType}
                      </span>
                    </div>
                  </div>
                )}
              </div>
              {/* Linked Issue - Only for downtimes */}
              {signal.signalType === 'downtime' && (
                <div className="flex flex-col gap-1">
                  <span className="text-neutral-subtle">Linked Issue</span>
                  {signal.downtime.issueId ? (
                    <Link
                      to={`/projects/${project.id}/issues/lists/all?issueId=${signal.downtime.issueId}`}
                      className="text-link-brand text-sm leading-5 font-normal underline cursor-pointer"
                    >
                      {signal.downtime.issueDescription}
                    </Link>
                  ) : (
                    <span className="text-neutral">None</span>
                  )}
                </div>
              )}
              {/* Description */}
              <div className="flex flex-col gap-1">
                <span className="text-neutral-subtle">Description</span>
                <p className="text-neutral">
                  {signal.signalType === 'issue' ? signal.issue.description : signal.downtime.issueDescription}
                </p>
              </div>

              {signal.signalType === 'issue' && (
                <>
                  {/* Discipline */}
                  <div className="flex flex-col gap-1">
                    <span className="text-neutral-subtle">Discipline</span>
                    <p className="text-neutral">Mechanical Works - Earthworks - Foundation/Shallow/Deep</p>
                  </div>
                  {/* Location */}
                  <div className="flex flex-col gap-1">
                    <span className="text-neutral-subtle">Location</span>
                    <p className="text-neutral">{truncatedLocationPath(locations, signal.locationId)}</p>
                  </div>

                  {/* More info */}
                  <div className="flex gap-4 sm:gap-12 grow flex-wrap">
                    {/* Author */}
                    <div className="flex flex-col gap-1">
                      <span className="text-neutral-subtle">Author</span>
                      <div className="flex gap-1">
                        <Avatar text="MO" size={'xs'} imgURL={''} />
                        <span className="text-neutral">Author</span>
                      </div>
                    </div>
                    {/* CreatedAt */}
                    <div className="flex flex-col gap-1">
                      <span className="text-neutral-subtle">Created</span>
                      <div className="flex gap-1">
                        <span className="text-neutral">27 March 2025</span>
                      </div>
                    </div>
                    {/* Impact */}
                    <div className="flex flex-col gap-1">
                      <span className="text-neutral-subtle">Impact</span>
                      <div className="flex gap-1">
                        <span className="text-neutral">Live Delay</span>
                      </div>
                    </div>
                    {/* Status */}
                    <div className="flex flex-col gap-1">
                      <span className="text-neutral-subtle">Status</span>
                      <div className="flex gap-1">
                        <span className="text-neutral">Pending approval</span>
                      </div>
                    </div>
                  </div>

                  {/* More info 2 */}
                  <div className="flex gap-4 sm:gap-12 grow flex-wrap">
                    {/* Author */}
                    <div className="flex flex-col gap-1">
                      <span className="text-neutral-subtle">Author</span>
                      <div className="flex gap-1">
                        <Avatar text="MO" size={'xs'} imgURL={''} />
                        <span className="text-neutral">Author</span>
                      </div>
                    </div>
                    {/* CreatedAt */}
                    <div className="flex flex-col gap-1">
                      <span className="text-neutral-subtle">Created</span>
                      <div className="flex gap-1">
                        <span className="text-neutral">27 March 2025</span>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Documents */}
              <div className="flex gap-2 flex-wrap">
                {signal.documents.map((document) => (
                  <FileThumbnail
                    key={document.id}
                    className="w-32"
                    caption={document.filename}
                    extension={document.extension}
                    fileId="5"
                    size="small"
                    thumbnailUrl={document.imageUrl?.s}
                  />
                ))}
              </div>
            </div>
          </CollapsableCard.Content>
        </>
      )}
    </CollapsableCard>
  );
}
