import type { PotentialChangeSchema } from "@shape-construction/api/src";
import { Avatar } from "@shape-construction/arch-ui";

type ChangeDetailsHeaderProps = {
    change: PotentialChangeSchema;
};
export const ChangeDetailsHeader: React.FC<ChangeDetailsHeaderProps> = ({ change }) => {
    return <div className="flex flex-col gap-2 px-4 py-6 border-b">
        <h1 className="text-xl leading-7 font-medium text-neutral-bold">{change.title}</h1>
        <div className="flex gap-6 text-xs leading-4 font-normal text-neutral-subtle">
            <div className="flex gap-2">
                <span>Author</span>
                <Avatar text="MO" size={'xs'} imgURL={''} />
                <span>Mohsin Y.</span>
            </div>
            <div className="flex gap-2">
                <span className="">Date</span>
                <span className="text-neutral">12 Feb 2026.</span>
            </div>
        </div>
    </div>
};
