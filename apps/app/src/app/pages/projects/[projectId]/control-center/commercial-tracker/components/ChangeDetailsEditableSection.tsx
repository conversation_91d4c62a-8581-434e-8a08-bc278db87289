import { useUpdatePotentialChange } from 'app/queries/control-center/commercial-tracker';
import { Category } from './columns/Category';
import { Comments } from './columns/Comments';
import { EarlyWarningSubmitted } from './columns/EarlyWarningSubmitted';
import { EstimatedCostImpact } from './columns/EstimatedCostImpact';
import { EstimatedScheduleImpact } from './columns/EstimatedScheduleImpact';
import { Priority } from './columns/Priority';
import { Status } from './columns/Status';
import type {
    PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
    PotentialChangeSchema,
} from '@shape-construction/api/src';
import { useCurrentProject } from 'app/contexts/currentProject';

type ChangeDetailsEditableSectionProps = {
    change: PotentialChangeSchema;
};

export const ChangeDetailsEditableSection: React.FC<ChangeDetailsEditableSectionProps> = ({ change }) => {
    const project = useCurrentProject();
    const { mutate: updateChange } = useUpdatePotentialChange();

    const onUpdatePotentialChangeRecord = (
        values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
    ) => {
        updateChange({
            potentialChangeId: change.id,
            projectId: project.id,
            data: values,
        });
    };

    return (
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 border-b w-full max-w-4xl px-4 py-6 min-h-56 overflow-x-hidden overflow-y-auto">
            <div className="flex flex-col gap-1">
                <span className="text-xs leading-4 font-normal text-neutral-subtle">Priority</span>
                <Priority onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} record={change} />
            </div>
            <div className="flex flex-col gap-1">
                <span className="text-xs leading-4 font-normal text-neutral-subtle">Category</span>
                <Category onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} record={change} />
            </div>
            <div className="flex flex-col gap-1">
                <span className="text-xs leading-4 font-normal text-neutral-subtle">Status</span>
                <Status onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} record={change} />
            </div>

            <div className="flex flex-col gap-1">
                <span className="text-xs leading-4 font-normal text-neutral-subtle">Cost estimative</span>
                <EstimatedCostImpact onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} record={change} />
            </div>
            <div className="flex flex-col gap-1">
                <span className="text-xs leading-4 font-normal text-neutral-subtle">Schedule impact estimative</span>
                <EstimatedScheduleImpact onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} record={change} />
            </div>
            <div className="flex flex-col gap-1">
                <span className="text-xs leading-4 font-normal text-neutral-subtle">Client notified</span>
                <EarlyWarningSubmitted onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} record={change} />
            </div>

            <div className="flex flex-col gap-1 col-span-2 sm:col-span-3">
                <span className="text-xs leading-4 font-normal text-neutral-subtle">Comment</span>
                <Comments onUpdatePotentialChangeRecord={onUpdatePotentialChangeRecord} record={change} />
            </div>
        </div>
    );
};
